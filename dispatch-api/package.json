{"name": "dispatch-api", "version": "1.0.0", "description": "NomadMD Dispatch API", "private": true, "main": "index.js", "scripts": {"prettier": "npx prettier -w .", "lint": "npx eslint --color .", "build": "npx tsc", "test": "jest", "test:watch": "jest --watch", "migrate": "npx knex migrate:latest", "migrate:staging": "dotenv -e .env.staging -- npx knex migrate:latest", "seed": "npx knex seed:run", "dev": "ts-node-dev --transpile-only src/index.ts", "dev:staging": "dotenv -e .env.staging -- ts-node-dev --transpile-only src/index.ts", "start": "node build/index.js", "start:staging": "dotenv -e .env.staging -- node build/index.js"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"@tsconfig/node16": "^16.1.1", "@types/aws-lambda": "^8.10.130", "@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.3", "@types/express-session": "^1.17.5", "@types/google-libphonenumber": "^7.4.23", "@types/jsonwebtoken": "^8.5.8", "@types/lodash": "^4.14.182", "@types/ms": "^0.7.31", "@types/node": "^16.11.46", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.2", "@types/sharp": "^0.30.4", "@types/validator": "^13.7.4", "@typescript-eslint/eslint-plugin": "^5.31.0", "@typescript-eslint/parser": "^5.31.0", "aws-cdk-lib": "^2.166.0", "constructs": "^10.4.2", "dotenv-cli": "^8.0.0", "esbuild": "^0.19.11", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "prettier": "^3.0.3", "sqlite3": "^5.0.10", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.137.0", "@aws-sdk/client-secrets-manager": "^3.485.0", "@aws-sdk/client-ssm": "^3.577.0", "@aws-sdk/s3-presigned-post": "^3.137.0", "@aws-sdk/s3-request-presigner": "^3.137.0", "@finix-payments/finix": "^3.0.1", "@googlemaps/google-maps-services-js": "^3.3.16", "@segment/analytics-node": "^2.2.1", "@sendgrid/mail": "^7.7.0", "@slack/webhook": "^6.1.0", "apollo-datasource": "^3.3.2", "apollo-server": "^3.10.0", "apollo-server-express": "^3.10.0", "bcrypt": "^5.0.1", "class-validator": "^0.14.0", "connect-session-knex": "^3.0.1", "cors": "^2.8.5", "dayjs": "^1.11.4", "db-errors": "^0.2.3", "dotenv": "^16.0.1", "express": "^4.18.2", "express-session": "^1.17.3", "fast-csv": "^5.0.1", "firebase-admin": "^12.1.1", "geolib": "^3.3.4", "get-orientation": "^1.1.2", "google-libphonenumber": "^3.2.30", "graphql": "^15.8.0", "handlebars": "^4.7.7", "hashids": "^2.2.10", "jsonwebtoken": "^9.0.2", "knex": "^2.5.1", "lodash": "^4.17.21", "ms": "^2.1.2", "node-fetch": "^2.6.7", "objection": "^3.0.1", "pdf-lib": "^1.17.1", "pg": "^8.7.3", "reflect-metadata": "^0.1.13", "sharp": "^0.30.7", "source-map-support": "^0.5.21", "stripe": "^12.5.0", "twilio": "^5.0.3", "type-graphql": "^1.1.1", "validator": "^13.7.0"}}