import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import onRequestCancelled from './on-request-cancelled';
import { Appointment, AppointmentRequest } from './sqldb';
import { getAppointmentRequest } from './sqldb/queries';
import { AppointmentRequestStatus, AppointmentStatus } from './sqldb/types';

interface CancelAppointmentRequestParams {
  sqldb: SqlDbSource;
  appointmentRequestId: number;
}

export async function cancelAppointmentRequest(
  params: CancelAppointmentRequestParams,
): Promise<AppointmentRequest | null> {
  const { sqldb, appointmentRequestId } = params;

  // lock the appointment request

  const releaseRequest = await acquireLock({
    sqldb,
    resources: [`appointment_requests:${appointmentRequestId}`],
  });

  if (!releaseRequest) {
    throw new ApolloError(
      'The appointment request is temporarily locked',
      'cancel-appointment-request:locked',
    );
  }

  try {
    const appointmentRequest = await getAppointmentRequest(sqldb.knex, {
      id: appointmentRequestId,
    }).withGraphFetched('constraints.appointments.participants');

    if (!appointmentRequest) {
      throw new ApolloError(
        'Invalid appointment request',
        'cancel-appointment-request:appointment-request',
      );
    }

    if (appointmentRequest.status === AppointmentRequestStatus.FULFILLED) {
      throw new ApolloError(
        'The appointment request has already been fulfilled',
        'cancel-appointment-request:fulfilled',
      );
    }

    try {
      await appointmentRequest
        .$query(sqldb.knex)
        .patch({ status: AppointmentRequestStatus.CANCELLED });

      // cancel pending appointments linked to this request

      await Appointment.query(sqldb.knex)
        .where({ status: AppointmentStatus.PENDING })
        .whereExists(
          Appointment.relatedQuery('constraint').where(
            'constraint.requestId',
            appointmentRequest.id,
          ),
        )
        .delete();
    } catch (err) {
      throw new ApolloError(
        'Error canceling the appointment request',
        'cancel-appointment-request:error',
      );
    }

    const cancelledRequest = await getAppointmentRequest(sqldb.knex, {
      id: appointmentRequestId,
    });
    if (cancelledRequest) {
      await onRequestCancelled({ sqldb, appointmentRequest: cancelledRequest });
    }

    return cancelledRequest ?? null;
  } finally {
    releaseRequest();
  }
}
