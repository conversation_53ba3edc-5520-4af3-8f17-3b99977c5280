import { SqlDbSource } from '../../datasources';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';
import onCompleted from './on-completed';
import onStarted from './on-started';
import onReset from './on-reset';
import onDeclined from './on-declined';

// Mock the dependencies
jest.mock('../reviews/check-in', () => ({
  birdeyeCheckIn: jest.fn(),
  reviewCheckIn: jest.fn(),
}));

jest.mock('../attentive/events', () => ({
  attentiveAppointmentCompleted: jest.fn(),
}));

jest.mock('../twilio/analytics', () => ({
  trackAppointment: jest.fn(),
}));

jest.mock('./log', () => ({
  logAppointment: jest.fn(),
}));

jest.mock('../dispatch/service', () => ({
  dispatch: jest.fn(),
}));

describe('Appointment Event System', () => {
  let mockSqlDb: SqlDbSource;
  let mockAppointment: Appointment;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSqlDb = {
      knex: {} as any,
    } as any;

    mockAppointment = {
      id: 1,
      status: AppointmentStatus.COMPLETED,
      startedAt: new Date(),
      constraintId: null,
      $relatedQuery: jest.fn(),
    } as any;
  });

  describe('onCompleted', () => {
    it('should call all completion-related functions when appointment is completed', async () => {
      const { birdeyeCheckIn, reviewCheckIn } = require('../reviews/check-in');
      const { attentiveAppointmentCompleted } = require('../attentive/events');
      const { trackAppointment } = require('../twilio/analytics');
      const { logAppointment } = require('./log');

      await onCompleted({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(birdeyeCheckIn).toHaveBeenCalledWith(mockAppointment, mockSqlDb);
      expect(reviewCheckIn).toHaveBeenCalledWith(mockAppointment, mockSqlDb);
      expect(attentiveAppointmentCompleted).toHaveBeenCalledWith(mockAppointment, mockSqlDb);
      expect(trackAppointment).toHaveBeenCalledWith({ appointment: mockAppointment, sqldb: mockSqlDb });
      expect(logAppointment).toHaveBeenCalledWith(mockAppointment, { sqldb: mockSqlDb });
    });

    it('should not call functions when appointment is not completed', async () => {
      const { birdeyeCheckIn } = require('../reviews/check-in');
      
      mockAppointment.status = AppointmentStatus.BOOKED;

      await onCompleted({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(birdeyeCheckIn).not.toHaveBeenCalled();
    });
  });

  describe('onStarted', () => {
    it('should call tracking and logging functions when appointment is started', async () => {
      const { trackAppointment } = require('../twilio/analytics');
      const { logAppointment } = require('./log');

      mockAppointment.status = AppointmentStatus.BOOKED;
      mockAppointment.startedAt = new Date();

      await onStarted({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(trackAppointment).toHaveBeenCalledWith({ appointment: mockAppointment, sqldb: mockSqlDb });
      expect(logAppointment).toHaveBeenCalledWith(mockAppointment, { sqldb: mockSqlDb });
    });

    it('should not call functions when appointment is not started', async () => {
      const { trackAppointment } = require('../twilio/analytics');
      
      mockAppointment.status = AppointmentStatus.BOOKED;
      mockAppointment.startedAt = null;

      await onStarted({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(trackAppointment).not.toHaveBeenCalled();
    });
  });

  describe('onReset', () => {
    it('should call tracking and logging functions when appointment is reset', async () => {
      const { trackAppointment } = require('../twilio/analytics');
      const { logAppointment } = require('./log');

      mockAppointment.status = AppointmentStatus.BOOKED;

      await onReset({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(trackAppointment).toHaveBeenCalledWith({ appointment: mockAppointment, sqldb: mockSqlDb });
      expect(logAppointment).toHaveBeenCalledWith(mockAppointment, { sqldb: mockSqlDb });
    });
  });

  describe('onDeclined', () => {
    it('should call tracking, logging, and dispatch functions', async () => {
      const { trackAppointment } = require('../twilio/analytics');
      const { logAppointment } = require('./log');
      const { dispatch } = require('../dispatch/service');

      const mockConstraint = { requestId: 123 };
      mockAppointment.constraintId = 456;
      mockAppointment.$relatedQuery = jest.fn().mockResolvedValue(mockConstraint);

      await onDeclined({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(trackAppointment).toHaveBeenCalledWith({ appointment: mockAppointment, sqldb: mockSqlDb });
      expect(logAppointment).toHaveBeenCalledWith(mockAppointment, { sqldb: mockSqlDb });
      expect(mockAppointment.$relatedQuery).toHaveBeenCalledWith('constraint', mockSqlDb.knex);
      expect(dispatch).toHaveBeenCalledWith({
        sqldb: mockSqlDb,
        appointmentRequestId: 123,
      });
    });

    it('should not call dispatch when no constraint exists', async () => {
      const { dispatch } = require('../dispatch/service');

      mockAppointment.constraintId = null;

      await onDeclined({ sqldb: mockSqlDb, appointment: mockAppointment });

      expect(dispatch).not.toHaveBeenCalled();
    });
  });
});
