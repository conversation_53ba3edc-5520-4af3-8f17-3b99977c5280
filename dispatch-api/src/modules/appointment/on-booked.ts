import { SqlDbSource } from '../../datasources';
import { subscribeUser } from '../attentive/subscribe';
import { createAppointmentForms } from '../emr/create-appointment-forms';
import { completeCheckout } from '../marketplace/checkout';
import { logAppointment } from './log';
import { notifyAppointmentPracitioners } from './notify';
import { refreshAppointmentParticipants } from './participant';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';
import { trackAppointment } from '../twilio/analytics';
import { appointmentBookedNotification } from '../twilio/notifications';

interface OnBookedOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onBooked({
  sqldb,
  appointment,
}: OnBookedOptions): Promise<void> {
  if (appointment.status === AppointmentStatus.BOOKED) {
    await createAppointmentForms({
      sqldb,
      appointmentId: appointment.id,
    });

    try {
      if (appointment.checkoutId) {
        const checkout = await completeCheckout({
          sqldb,
          checkoutId: appointment.checkoutId,
        });

        appointment.$setRelated('checkout', checkout);
      }
    } catch (err) {
      console.log(err);
    }

    await refreshAppointmentParticipants({ sqldb, appointment });

    trackAppointment({ appointment, sqldb });
    notifyAppointmentPracitioners({ sqldb, appointment });
    logAppointment(appointment, { sqldb });
    subscribeUser({ appointment, sqldb });
    appointmentBookedNotification({ sqldb, appointment });
  }
}
