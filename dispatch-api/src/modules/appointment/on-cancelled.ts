import { SqlDbSource } from '../../datasources';
import { logAppointment } from './log';
import { notifyAppointmentPracitioners } from './notify';
import { refreshAppointmentParticipants } from './participant';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';
import { trackAppointment } from '../twilio/analytics';

interface OnCancelledOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onCancelled({
  sqldb,
  appointment,
}: OnCancelledOptions): Promise<void> {
  if (appointment.status === AppointmentStatus.CANCELLED) {
    await refreshAppointmentParticipants({ sqldb, appointment });
    notifyAppointmentPracitioners({ sqldb, appointment });
  }

  trackAppointment({ appointment, sqldb });
  logAppointment(appointment, { sqldb });
}
