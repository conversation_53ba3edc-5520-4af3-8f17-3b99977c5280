import { SqlDbSource } from '../../datasources';
import { birdeyeCheckIn, reviewCheckIn } from '../reviews/check-in';
import { attentiveAppointmentCompleted } from '../attentive/events';
import { trackAppointment } from '../twilio/analytics';
import { logAppointment } from './log';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';

interface OnCompletedOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onCompleted({
  sqldb,
  appointment,
}: OnCompletedOptions): Promise<void> {
  if (appointment.status === AppointmentStatus.COMPLETED) {
    await birdeyeCheckIn(appointment, sqldb);
    await reviewCheckIn(appointment, sqldb);
    await attentiveAppointmentCompleted(appointment, sqldb);
    await trackAppointment({ appointment, sqldb });
    await logAppointment(appointment, { sqldb });
  }
}
