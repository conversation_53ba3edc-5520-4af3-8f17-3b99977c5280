import { SqlDbSource } from '../../datasources';
import { notifyAppointmentPracitioners } from './notify';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';

interface OnBookedOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onPending({
  sqldb,
  appointment,
}: OnBookedOptions): Promise<void> {
  if (appointment.status === AppointmentStatus.PENDING) {
    await notifyAppointmentPracitioners({ sqldb, appointment });
  }
}
