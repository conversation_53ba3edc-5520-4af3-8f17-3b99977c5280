import { SqlDbSource } from '../../datasources';
import { dispatch } from '../dispatch/service';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';
import { AppointmentRequestStatus } from './sqldb/types';

interface OnRequestApprovedOptions {
  sqldb: SqlDbSource;
  appointmentRequest: AppointmentRequest;
}

export default async function onRequestApproved({
  sqldb,
  appointmentRequest,
}: OnRequestApprovedOptions): Promise<void> {
  if (appointmentRequest.status === AppointmentRequestStatus.PENDING) {
    dispatch({ sqldb, appointmentRequestId: appointmentRequest.id });
  }

  await logAppointmentRequest(appointmentRequest, { sqldb });
}
