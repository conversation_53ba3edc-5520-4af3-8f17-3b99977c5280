import { SqlDbSource } from '../../datasources';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';

interface OnRequestCancelledOptions {
  sqldb: SqlDbSource;
  appointmentRequest: AppointmentRequest;
}

export default async function onRequestCancelled({
  sqldb,
  appointmentRequest,
}: OnRequestCancelledOptions): Promise<void> {
  await logAppointmentRequest(appointmentRequest, { sqldb });

  // TODO: Add notification logic when implemented
  // This is where appointment request cancellation notifications would be sent
}
