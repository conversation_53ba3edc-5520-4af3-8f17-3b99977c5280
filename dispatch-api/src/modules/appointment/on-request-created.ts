import { SqlDbSource } from '../../datasources';
import { dispatch } from '../dispatch/service';
import { appointmentRequestNotification } from '../twilio/notifications';
import { trackAppointmentRequest } from '../twilio/analytics';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';
import { AppointmentRequestStatus } from './sqldb/types';

interface OnRequestCreatedOptions {
  sqldb: SqlDbSource;
  appointmentRequest: AppointmentRequest;
  marketplaceUserId?: number;
}

export default async function onRequestCreated({
  sqldb,
  appointmentRequest,
  marketplaceUserId,
}: OnRequestCreatedOptions): Promise<void> {
  await logAppointmentRequest(appointmentRequest, { sqldb });

  if (appointmentRequest.status === AppointmentRequestStatus.PENDING) {
    dispatch({ sqldb, appointmentRequestId: appointmentRequest.id });
  }

  await appointmentRequestNotification({
    sqldb,
    appointmentRequest,
    marketplaceUserId,
  });

  await trackAppointmentRequest({ request: appointmentRequest, sqldb });
}
