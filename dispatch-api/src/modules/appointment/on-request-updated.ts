import { SqlDbSource } from '../../datasources';
import { trackAppointmentRequest } from '../twilio/analytics';
import { logAppointmentRequest } from './log';
import { AppointmentRequest } from './sqldb';

interface OnRequestUpdatedOptions {
  sqldb: SqlDbSource;
  appointmentRequest: AppointmentRequest;
}

export default async function onRequestUpdated({
  sqldb,
  appointmentRequest,
}: OnRequestUpdatedOptions): Promise<void> {
  await trackAppointmentRequest({ request: appointmentRequest, sqldb });
  await logAppointmentRequest(appointmentRequest, { sqldb, isUpdate: true });
}
