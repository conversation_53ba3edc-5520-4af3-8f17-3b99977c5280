import { SqlDbSource } from '../../datasources';
import { trackAppointment } from '../twilio/analytics';
import { logAppointment } from './log';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';

interface OnResetOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onReset({
  sqldb,
  appointment,
}: OnResetOptions): Promise<void> {
  if (appointment.status === AppointmentStatus.BOOKED) {
    trackAppointment({ appointment, sqldb });
    logAppointment(appointment, { sqldb });
  }
}
