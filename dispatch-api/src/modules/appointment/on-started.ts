import { SqlDbSource } from '../../datasources';
import { trackAppointment } from '../twilio/analytics';
import { logAppointment } from './log';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';

interface OnStartedOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onStarted({
  sqldb,
  appointment,
}: OnStartedOptions): Promise<void> {
  if (
    appointment.status === AppointmentStatus.BOOKED &&
    appointment.startedAt
  ) {
    await trackAppointment({ appointment, sqldb });
    await logAppointment(appointment, { sqldb });
  }
}
