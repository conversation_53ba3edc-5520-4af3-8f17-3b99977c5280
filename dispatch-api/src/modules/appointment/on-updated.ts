import { SqlDbSource } from '../../datasources';
import { createAppointmentForms } from '../emr/create-appointment-forms';
import { trackAppointmentUpdated } from '../twilio/analytics';
// import { completeCheckout } from '../marketplace/checkout';
import { logAppointment } from './log';
import { notifyAppointmentPracitioners } from './notify';
import { refreshAppointmentParticipants } from './participant';
import { Appointment } from './sqldb';
import { AppointmentStatus } from './sqldb/types';

interface OnBookedOptions {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export default async function onUpdated({
  sqldb,
  appointment,
}: OnBookedOptions): Promise<void> {
  if (appointment.status === AppointmentStatus.BOOKED) {
    await createAppointmentForms({
      sqldb,
      appointmentId: appointment.id,
    });

    // try {
    //   if (appointment.checkoutId) {
    //     const checkout = await completeCheckout({
    //       sqldb,
    //       checkoutId: appointment.checkoutId,
    //     });

    //     appointment.$setRelated('checkout', checkout);
    //   }
    // } catch (err) {
    //   console.log(err);
    // }

    await refreshAppointmentParticipants({ sqldb, appointment });

    trackAppointmentUpdated({ appointment, sqldb });
    notifyAppointmentPracitioners({ sqldb, appointment, isUpdate: true });
    logAppointment(appointment, { sqldb, isUpdate: true });
  }
}
