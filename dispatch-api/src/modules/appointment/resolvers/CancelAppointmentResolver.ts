import { ApolloError, ForbiddenError } from 'apollo-server';
import { filter } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { User } from '../../user/sqldb';
import { cancelAppointment } from '../cancel-appointment';
import { authorizeAppointment } from '../common';
import { Appointment } from '../sqldb';
import { ParticipantType } from '../sqldb/types';
import CancelAppointmentInput from './CancelAppointmentInput';

@Resolver()
export default class CancelAppointmentResolver {
  @Mutation(() => Appointment, { nullable: true })
  async cancelAppointment(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('input') input: CancelAppointmentInput,
  ): Promise<Appointment | null> {
    const appointment = await dataSources.sqldb.appointment(
      intFromID(input.appointmentId),
    );

    if (!appointment) {
      throw new ApolloError('Invalid appointment id', 'appointment:id');
    }

    const practitioners = filter(appointment.participants ?? [], {
      type: ParticipantType.PRACTITIONER,
    }).map((p) => p.profile);

    if (!practitioners.some((profile) => authorizeAppointment(user, profile))) {
      throw new ForbiddenError('Not authorized (cancelAppointment)');
    }

    const { refundCheckout } = input;

    return cancelAppointment({
      sqldb: dataSources.sqldb,
      appointmentId: appointment.id,
      refundCheckout,
    });
  }
}
