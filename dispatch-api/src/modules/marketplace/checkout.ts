import { ApolloError } from 'apollo-server';
import { find } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { getPackageItemIdsForCheckout } from '../appointment/checkout';
import { ParticipantType } from '../appointment/sqldb/types';
import { PackageItem } from '../membership/sqldb';
import { completeCheckoutPayment } from '../payment/complete-checkout-payment';
import { completeCheckoutPayout } from '../payment/complete-checkout-payout';
import { PaymentMethod } from '../payment/payment';
import { Checkout } from '../payment/sqldb';

interface CompleteCheckoutParams {
  sqldb: SqlDbSource;
  checkoutId: number;
  paymentMethod?: PaymentMethod;
  expectedAmount?: number;
}

export async function completeCheckout(
  params: CompleteCheckoutParams,
): Promise<Checkout> {
  const { sqldb, checkoutId, paymentMethod, expectedAmount } = params;

  const checkout = await sqldb.checkout(checkoutId);

  if (!checkout) {
    throw new ApolloError('Invalid checkout', 'complete-checkout:checkout');
  }

  const marketplace = await sqldb.marketplace(checkout.marketplaceId);

  let organizationId: number | null = null;
  let paymentAccountId: number | null = null;
  const tags: { [key: string]: string } = {};

  const [appointmentRequest, appointment] = await Promise.all([
    checkout.$relatedQuery('appointmentRequest', sqldb.knex),
    checkout
      .$relatedQuery('appointment', sqldb.knex)
      .withGraphJoined('participants.profile'),
  ]);

  if (appointmentRequest || appointment) {
    if (!appointment) {
      throw new ApolloError(
        'Appointment must be dispatched before completing checkout',
        'complete-checkout:appointment',
      );
    }

    const practitioner = find(appointment.participants ?? [], {
      type: ParticipantType.PRACTITIONER,
    });

    const orgId = practitioner?.profile?.organizationId;
    const organization = await sqldb.organization(orgId);

    if (!organization?.paymentAccountId) {
      throw new ApolloError(
        'Organization does not have a payment account',
        'complete-appointment-checkout:payment-account',
      );
    }

    organizationId = organization.id;
    paymentAccountId = organization.paymentAccountId;
    tags.appointmentId = String(appointment.id);
  } else {
    paymentAccountId = marketplace?.paymentAccountId ?? null;
    // payoutAccountId = null;
  }

  if (!paymentAccountId) {
    throw new ApolloError(
      'Payment account not configured',
      'complete-checkout:payment-account',
    );
  }

  let result;

  if (paymentMethod || checkout.paymentInstrument) {
    result = await completeCheckoutPayment({
      sqldb,
      checkoutId,
      paymentAccountId,
      paymentMethod,
      expectedAmount,
      tags,
      feeProfile: {
        fixed: marketplace?.feeProfileFixed,
        basisPoints: marketplace?.feeProfileBasisPoints,
      },
    });
  }

  if (marketplace?.paymentAccountId) {
    result = await completeCheckoutPayout({
      sqldb,
      checkoutId,
      paymentAccountId,
      payoutAccountId: marketplace.paymentAccountId,
      tags,
    });
  }

  if (organizationId) {
    // ensure primaryOrganizationId is set on the checkout package items

    const packageItemIds = await getPackageItemIdsForCheckout({
      sqldb,
      checkoutId,
    });

    if (packageItemIds.length > 0) {
      await PackageItem.query(sqldb.knex)
        .whereIn('id', packageItemIds)
        .whereNull('primaryOrganizationId')
        .update({ primaryOrganizationId: organizationId });
    }
  }

  checkout.appointment?.$setRelated('checkout', result);

  return result ?? checkout;
}

// interface CompletePayoutParams {
//   sqldb: SqlDbSource;
//   checkoutId: number;
// }

// async function getPayoutAccount(
//   params: CompletePayoutParams,
// ): Promise<PaymentAccount | undefined> {
//   const { sqldb, checkoutId } = params;

//   const checkout = await sqldb.checkout(checkoutId);

//   if (!checkout) {
//     throw new ApolloError('Invalid checkout', 'complete-payouts:checkout');
//   }

//   // todo: for each item, get baseDef -> marketplace -> paymentAccount,
//   //   map paymentAccount -> total transfer amount
//   //   create payout, or reverse

//   const packageItems = (checkout.items ?? [])
//     .filter((item) => item.type === CheckoutItemType.PACKAGE_CREDIT)
//     .map((checkoutItem) => ({
//       checkoutItem,
//       ...decodePackageItemKey(checkoutItem.key),
//     }))
//     .filter(({ baseDefId }) => Boolean(baseDefId));

//   if (packageItems.length > 0) {
//     const baseDef = await sqldb.procedureBaseDef(packageItems[0].baseDefId);
//     const marketplace = await sqldb.marketplace(baseDef?.marketplaceId);

//     const payoutAccount = await sqldb.paymentAccount(
//       marketplace?.paymentAccountId ?? undefined,
//     );

//     if (payoutAccount) {
//       return payoutAccount;
//     }
//   }
// }
