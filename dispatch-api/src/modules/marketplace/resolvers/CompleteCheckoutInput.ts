import { Field, ID, InputType, Int } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import PaymentMethodInput from '../../payment/resolvers/PaymentMethodInput';

@InputType()
export default class CompleteCheckoutInput {
  @Field(() => ID)
  @IsNumberID()
  checkoutId!: string;

  @Field(() => PaymentMethodInput, { nullable: true })
  paymentMethod?: PaymentMethodInput;

  @Field(() => Int, { nullable: true })
  expectedAmount?: number;
}
