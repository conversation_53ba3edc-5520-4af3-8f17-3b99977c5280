import { IsHexColor, IsUrl, Length, MaxLength, Min } from 'class-validator';
import { Field, ID, InputType, Int } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import { Marketplace } from '../sqldb';

@InputType()
export default class MarketplaceInput implements Partial<Marketplace> {
  @Field({ nullable: true })
  @Length(3, 100)
  name?: string;

  @Field(() => ID, { nullable: true })
  @IsNumberID()
  groupId?: number;

  @Field({ nullable: true })
  requireDispatchApproval?: boolean;

  @Field({ nullable: true })
  requirePractitionerApproval?: boolean;

  @Field({ nullable: true })
  @MaxLength(255)
  @IsUrl({
    protocols: ['https'],
    require_valid_protocol: true,
    require_protocol: true,
    require_host: true,
  })
  slackWebhookUrl?: string;

  @Field({ nullable: true })
  @MaxLength(255)
  reviewsIoKey?: string;

  @Field({ nullable: true })
  @MaxLength(255)
  reviewsIoStoreId?: string;

  @Field(() => Int, { nullable: true })
  @Min(0)
  feeProfileFixed?: number;

  @Field(() => Int, { nullable: true })
  @Min(0)
  feeProfileBasisPoints?: number;

  @Field(() => String, { nullable: true })
  @MaxLength(255)
  @IsHexColor()
  primaryColor?: string | null;

  @Field(() => String, { nullable: true })
  @Length(1, 50)
  logoToken?: string;
}
