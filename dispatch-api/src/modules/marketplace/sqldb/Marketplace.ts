import { Model, RelationMappings } from 'objection';
import { Field, ID, ObjectType } from 'type-graphql';
import { AppointmentRequest } from '../../appointment/sqldb';
import { AttentiveIntegration } from '../../attentive/sqldb';
import { ArchivableModel, EncryptedKey } from '../../common/sqldb';
import { MembershipDefinition, Package } from '../../membership/sqldb';
import { Organization } from '../../organization/sqldb';
import { PaymentAccount } from '../../payment/sqldb';
import { ProcedureBaseDefinitionGroup } from '../../procedure-base-def-group/sqldb';
import { ProcedureBaseDefinition } from '../../procedure/sqldb';
import { Role } from '../../role/sqldb';
import {
  SegmentIntegration,
  SendgridIntegration,
  TwilioIntegration,
} from '../../twilio/sqldb';
import MarketplaceGroup from './MarketplaceGroup';
import { MarketplaceFields } from './types';

@ObjectType()
export default class Marketplace
  extends ArchivableModel
  implements MarketplaceFields
{
  @Field(() => ID)
  readonly id!: number;

  @Field()
  name!: string;

  @Field(() => ID)
  groupId!: number;

  @Field({ nullable: true })
  reviewsIoStoreId?: string;

  @Field(() => ID, { nullable: true })
  navigationGroupId?: number;

  @Field(() => ID, { nullable: true })
  paymentAccountId?: number | null;

  @Field(() => String, { nullable: true })
  logo?: string | null;

  @Field(() => String, { nullable: true })
  primaryColor?: string | null;

  requireDispatchApproval?: boolean;
  requirePractitionerApproval?: boolean;
  slackWebhookUrl?: string;
  reviewsGroupId?: number;
  notificationsGroupId?: number;
  feeProfileFixed?: number;
  feeProfileBasisPoints?: number;

  organizations?: Organization[];
  roles?: Role[];
  procedureBaseDefs?: ProcedureBaseDefinition[];
  group?: MarketplaceGroup;
  appointmentRequests?: AppointmentRequest[];
  procedureBaseDefGroups?: ProcedureBaseDefinitionGroup[];
  packages?: Package[];
  membershipDefinitions?: MembershipDefinition[];
  paymentAccounts?: PaymentAccount[];
  attentive?: AttentiveIntegration;
  sendgrid?: SendgridIntegration;
  twilio?: TwilioIntegration;
  segment?: SegmentIntegration;
  reviewsIoKey?: string; // deprecated - will be removed
  reviewsIoKeyId?: number;
  encryptedReviewsIoKey?: EncryptedKey;

  static tableName = 'marketplaces';

  static relationMappings = (): RelationMappings => ({
    organizations: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../organization/sqldb').Organization,
      join: {
        from: 'marketplaces.id',
        through: {
          from: 'marketplacesOrganizations.marketplaceId',
          to: 'marketplacesOrganizations.organizationId',
        },
        to: 'organizations.id',
      },
    },
    roles: {
      relation: Model.HasManyRelation,
      modelClass: require('../../role/sqldb').Role,
      join: {
        from: 'marketplaces.id',
        to: 'roles.marketplaceId',
      },
    },
    procedureBaseDefs: {
      relation: Model.HasManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureBaseDefinition,
      join: {
        from: 'marketplaces.id',
        to: 'procedureBaseDefinitions.marketplaceId',
      },
    },
    group: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').MarketplaceGroup,
      join: {
        from: 'marketplaces.groupId',
        to: 'marketplaceGroups.id',
      },
    },
    appointmentRequests: {
      relation: Model.HasManyRelation,
      modelClass: require('../../appointment/sqldb').AppointmentRequest,
      join: {
        from: 'marketplaces.id',
        to: 'appointmentRequests.marketplaceId',
      },
    },
    packages: {
      relation: Model.HasManyRelation,
      modelClass: require('../../membership/sqldb').Package,
      join: {
        from: 'marketplaces.id',
        to: 'packages.marketplaceId',
      },
    },
    membershipDefinitions: {
      relation: Model.HasManyRelation,
      modelClass: require('../../membership/sqldb').MembershipDefinition,
      join: {
        from: 'marketplaces.id',
        to: 'membershipDefinitions.marketplaceId',
      },
    },
    attentive: {
      relation: Model.HasOneRelation,
      modelClass: require('../../attentive/sqldb').AttentiveIntegration,
      join: {
        from: 'marketplaces.id',
        to: 'attentiveIntegrations.marketplaceId',
      },
    },
    sendgrid: {
      relation: Model.HasOneRelation,
      modelClass: require('../../twilio/sqldb').SendgridIntegration,
      join: {
        from: 'marketplaces.id',
        to: 'sendgridIntegrations.marketplaceId',
      },
    },
    paymentAccounts: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../payment/sqldb').PaymentAccount,
      join: {
        from: 'marketplaces.id',
        through: {
          from: 'marketplacesPaymentAccounts.marketplaceId',
          to: 'marketplacesPaymentAccounts.paymentAccountId',
        },
        to: 'paymentAccounts.id',
      },
    },
    twilio: {
      relation: Model.HasOneRelation,
      modelClass: require('../../twilio/sqldb').TwilioIntegration,
      join: {
        from: 'marketplaces.id',
        to: 'twilioIntegrations.marketplaceId',
      },
    },
    segment: {
      relation: Model.HasOneRelation,
      modelClass: require('../../twilio/sqldb').SegmentIntegration,
      join: {
        from: 'marketplaces.id',
        to: 'segmentIntegrations.marketplaceId',
      },
    },
    encryptedReviewsIoKey: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../common/sqldb').EncryptedKey,
      join: {
        from: 'marketplaces.reviewsIoKeyId',
        to: 'encryptedKeys.id',
      },
    },
  });
}
