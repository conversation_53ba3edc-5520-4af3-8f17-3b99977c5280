import { Currency } from '@finix-payments/finix/lib/model/currency';
import { Transfer } from '@finix-payments/finix/lib/model/transfer';
import { ApolloError } from 'apollo-server';
import { PartialModelObject } from 'objection';
import { getFinix } from '.';
import { type SqlDbSource } from '../../../datasources';
import { generateToken } from '../../common/random';
import { acquireLock } from '../../distributed-lock/lock';
import { refreshCheckoutTotals } from '../common';
import { onPaymentAccepted } from '../on-payment-accepted';
import { type PaymentMethod } from '../payment';
import { Payment } from '../sqldb';
import {
  PaymentAccountPlatform,
  PaymentStatus,
  PaymentType,
} from '../sqldb/types';
import { upsertPaymentInstrument } from './payment-instrument';

interface CreatePaymentParams {
  sqldb: SqlDbSource;
  checkoutId: number;
  paymentAccountId: number;
  type: PaymentType;
  amount: number;
  paymentMethod?: PaymentMethod;
  fee?: number;
  tags?: { [key: string]: string };
}

export async function createPayment(
  params: CreatePaymentParams,
): Promise<Payment> {
  const {
    sqldb,
    checkoutId,
    paymentAccountId,
    type,
    amount,
    paymentMethod,
    fee = 0,
    tags,
  } = params;

  const checkout = await sqldb.checkout(checkoutId);

  if (!checkout) {
    throw new ApolloError('Invalid checkout', 'create-payment:checkout');
  }

  if (type === PaymentType.CREDIT) {
    let paymentInstrument;

    if (paymentMethod) {
      paymentInstrument = await upsertPaymentInstrument({
        sqldb,
        paymentMethod,
      });
    } else {
      paymentInstrument = await sqldb.paymentInstrument(
        checkout.paymentInstrumentId,
      );
    }

    if (!paymentInstrument) {
      throw new ApolloError(
        'Invalid payment instrument',
        'create-payment:payment-instrument',
      );
    }

    const paymentAccount = await sqldb.paymentAccount(paymentAccountId);

    if (
      !paymentAccount ||
      paymentAccount.platform !== PaymentAccountPlatform.FINIX
    ) {
      throw new ApolloError(
        'Invalid payment account',
        'create-payment:payment-account',
      );
    }

    if (!paymentAccount.enabled) {
      throw new ApolloError(
        'The configured payment account is disabled',
        'create-payment:payment-account-disabled',
      );
    }

    try {
      const transfer = await getFinix().sdk.Transfers.create({
        amount,
        currency: Currency.Usd,
        source: paymentInstrument.instrumentId,
        merchant: paymentAccount.finix?.merchantId,
        fee,
        tags,
        idempotencyId: await generateToken(),
      });

      const status = mapTransferState(transfer.state as Transfer.StateEnum);

      let payment: PartialModelObject<Payment> = {
        status,
        checkoutId: checkout.id,
        paymentAccountId,
        paymentInstrumentId: paymentInstrument.id,
        processor: PaymentAccountPlatform.FINIX,
        transactionId: transfer.id,
        type: PaymentType.CREDIT,
        amountRequested: amount,
        description: `Payment from card **** ${paymentInstrument.lastFour}`,
        fee,
      };

      if (status === PaymentStatus.FAILED) {
        payment = {
          ...payment,
          description: `${payment.description}: ${transfer.failureMessage}`,
          amount: 0,
        };
      } else {
        payment = {
          ...payment,
          amount,
        };
      }

      return await Payment.query(sqldb.knex).insert(payment);
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  throw new ApolloError('Invalid payment type', 'create-payment:type');
}

interface RefundPaymentParams {
  sqldb: SqlDbSource;
  paymentId: number;
  refundAmount?: number;
}

export async function refundPayment(
  params: RefundPaymentParams,
): Promise<Payment> {
  const { sqldb, paymentId } = params;

  const payment = await sqldb.payment(paymentId);

  if (!payment) {
    throw new ApolloError('Invalid payment', 'refund-payment:payment');
  }

  if (payment.status !== PaymentStatus.ACCEPTED) {
    throw new ApolloError(
      'Cannot refund payment',
      'refund-payment:payment-status',
    );
  }

  const amountReversed = payment.amountReversed ?? 0;
  const maxAmount = payment.amount - amountReversed;
  const refundAmount = params.refundAmount ?? maxAmount;

  if (refundAmount > maxAmount) {
    throw new ApolloError(
      'Cannot refund more than the payment amount',
      'refund-payment:amount',
    );
  }

  if (payment.type === PaymentType.CREDIT) {
    if (
      payment.processor !== PaymentAccountPlatform.FINIX ||
      !payment.transactionId
    ) {
      throw new ApolloError(
        'Unknown transaction',
        'refund-payment:transaction',
      );
    }

    const paymentInstrument = await sqldb.paymentInstrument(
      payment.paymentInstrumentId,
    );

    if (!paymentInstrument) {
      throw new ApolloError(
        'Invalid payment instrument',
        'refund-payment:payment-instrument',
      );
    }

    const refund = await getFinix().sdk.Transfers.createTransferReversal(
      payment.transactionId,
      {
        refundAmount,
      },
    );

    const status = mapTransferState(refund.state as Transfer.StateEnum);

    let refundPayment: PartialModelObject<Payment> = {
      checkoutId: payment.checkoutId,
      paymentInstrumentId: payment.paymentInstrumentId,
      processor: PaymentAccountPlatform.FINIX,
      transactionId: refund.id,
      type: PaymentType.CREDIT,
      status,
      description: `Refund to card **** ${paymentInstrument.lastFour}`,
      amountRequested: refundAmount * -1,
    };

    if (status === PaymentStatus.FAILED) {
      refundPayment = {
        ...refundPayment,
        description: `${payment.description}: ${refund.failureMessage}`,
        amount: 0,
      };
    } else {
      refundPayment = {
        ...refundPayment,
        amount: refundAmount * -1,
      };
    }

    await Payment.query(sqldb.knex).insert(refundPayment);
  }

  await payment.$query(sqldb.knex).patch({
    amountReversed: amountReversed + refundAmount,
  });

  sqldb.invalidate('payment', [paymentId]);
  return (await sqldb.payment(paymentId)) ?? payment;
}

const mapTransferState = (state?: Transfer.StateEnum): PaymentStatus => {
  if (state === Transfer.StateEnum.Succeeded) {
    return PaymentStatus.ACCEPTED;
  } else if (state === Transfer.StateEnum.Pending) {
    return PaymentStatus.PENDING;
  }
  return PaymentStatus.FAILED;
};

interface RefreshPaymentTransferParams {
  sqldb: SqlDbSource;
  transfer: Transfer;
}

export async function refreshPaymentTransfer(
  params: RefreshPaymentTransferParams,
): Promise<boolean> {
  const { sqldb, transfer } = params;
  const finix = getFinix();

  if (!transfer?.id) {
    return false;
  }

  let payment = await Payment.query(sqldb.knex).findOne(
    'transactionId',
    transfer.id,
  );

  if (!payment) {
    return false;
  }

  const release = await acquireLock({
    sqldb,
    resources: [`checkout:${payment.checkoutId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The checkout is temporarily locked',
      'update-checkout:locked',
    );
  }

  try {
    payment = await Payment.query(sqldb.knex).findById(payment.id);

    if (!payment) {
      return false;
    }

    const status = mapTransferState(transfer.state as Transfer.StateEnum);
    const paymentUpdate: PartialModelObject<Payment> = { status };

    if (status === PaymentStatus.FAILED) {
      paymentUpdate.amount = 0;

      if (payment.status === PaymentStatus.PENDING) {
        paymentUpdate.description = `${payment.description}: ${transfer.failureMessage}`;

        if (transfer.type === Transfer.TypeEnum.Reversal) {
          const link = transfer.links?.parent?.href;

          if (link) {
            const parent = await finix.fetch(link);

            if (parent?.id) {
              const parentPayment = await Payment.query(sqldb.knex).findOne(
                'transactionId',
                parent.id,
              );

              if (parentPayment?.amountReversed) {
                await Payment.query(sqldb.knex)
                  .findById(parentPayment.id)
                  .patch({
                    amountReversed: Math.max(
                      parentPayment.amountReversed -
                        (transfer.amountRequested ?? 0) / 100,
                      0,
                    ),
                  });
              }
            }
          }
        }
      }
    }

    const previousStatus = payment.status;

    const updatedPayment = await payment
      .$query(sqldb.knex)
      .patchAndFetch(paymentUpdate);

    await refreshCheckoutTotals({ sqldb, checkoutId: payment.checkoutId });

    if (
      status === PaymentStatus.ACCEPTED &&
      previousStatus !== PaymentStatus.ACCEPTED
    ) {
      await onPaymentAccepted({ sqldb, payment: updatedPayment });
    }

    return true;
  } finally {
    await release();
  }
}
