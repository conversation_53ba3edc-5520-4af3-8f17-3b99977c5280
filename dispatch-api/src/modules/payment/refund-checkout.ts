import { ApolloError } from 'apollo-server';
import { filter } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import {
  getCheckoutBalance,
  getCheckoutTotal,
  refreshCheckoutTotals,
} from './common';
import { refundPayment } from './finix/payment';
import { Checkout } from './sqldb';
import { PaymentStatus } from './sqldb/types';

interface RefundCheckoutParams {
  sqldb: SqlDbSource;
  checkoutId: number;
  refundAmount: number;
  balanceAfterRefund: number;
}

export async function refundCheckoutWithLock(
  params: RefundCheckoutParams,
): Promise<Checkout> {
  const { sqldb, checkoutId } = params;

  const release = await acquireLock({
    sqldb,
    resources: [`checkout:${checkoutId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The checkout is temporarily locked',
      'refund-checkout:locked',
    );
  }

  try {
    return refundCheckout(params);
  } finally {
    await release();
  }
}

export async function refundCheckout(
  params: RefundCheckoutParams,
): Promise<Checkout> {
  const { sqldb, checkoutId, refundAmount, balanceAfterRefund } = params;

  sqldb.invalidate('checkout', [checkoutId]);
  const checkout = await sqldb.checkout(checkoutId);

  if (!checkout) {
    throw new ApolloError('Invalid checkout', 'refund-checkout:checkout');
  }

  if (checkout.voidedAt) {
    throw new ApolloError('Checkout has been voided', 'refund-checkout:voided');
  }

  const total = await getCheckoutTotal({
    sqldb,
    checkout,
  });

  const balance = await getCheckoutBalance({
    sqldb,
    checkout,
  });

  const paid = total - balance;

  if (
    refundAmount < 1 ||
    refundAmount > paid ||
    balanceAfterRefund !== balance + refundAmount
  ) {
    throw new ApolloError('Invalid refund amount', 'refund-checkout:amount');
  }

  const payments = filter(
    checkout.payments,
    ({ status, amount }) => status === PaymentStatus.ACCEPTED && amount > 0,
  ).sort((a, b) => b.id - a.id);

  let remaining = refundAmount;

  for (const payment of payments) {
    if (remaining > 0) {
      const amount = Math.min(remaining, payment.amount);

      try {
        await refundPayment({
          sqldb,
          paymentId: payment.id,
          refundAmount: amount,
        });

        remaining -= amount;
      } catch {
        // ignore
      }
    } else {
      break;
    }
  }

  await refreshCheckoutTotals({ sqldb, checkoutId });

  sqldb.invalidate('checkout', [checkoutId]);
  return (await sqldb.checkout(checkoutId)) ?? checkout;
}
