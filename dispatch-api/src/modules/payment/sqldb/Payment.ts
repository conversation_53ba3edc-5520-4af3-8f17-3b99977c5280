import { Model, Pojo, RelationMappings } from 'objection';
import { Field, ID, Int, ObjectType, registerEnumType } from 'type-graphql';
import { ArchivableModel, BaseModel } from '../../common/sqldb';
import {
  PaymentAccountPlatform,
  PaymentFields,
  PaymentStatus,
  PaymentType,
} from './types';

registerEnumType(PaymentType, {
  name: 'PaymentType',
});

registerEnumType(PaymentStatus, {
  name: 'PaymentStatus',
});

@ObjectType()
export default class Payment extends ArchivableModel implements PaymentFields {
  @Field(() => ID)
  readonly id!: number;

  @Field(() => PaymentType)
  type!: PaymentType;

  @Field(() => PaymentStatus)
  status!: PaymentStatus;

  @Field()
  description!: string;

  @Field(() => Int)
  amount!: number;

  @Field(() => Int)
  amountRequested!: number;

  @Field(() => Int)
  fee!: number;

  @Field(() => Int, { nullable: true })
  amountReversed?: number;

  processor?: PaymentAccountPlatform;
  transactionId?: string; // e.g., the finix transfer id, or authorization id
  paymentInstrumentId?: number;
  paymentAccountId?: number;
  checkoutId!: number;

  static tableName = 'payments';

  static relationMappings = (): RelationMappings => ({
    checkout: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').Checkout,
      join: {
        from: 'payments.checkoutId',
        to: 'checkouts.id',
      },
    },
    paymentAccount: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').PaymentAccount,
      join: {
        from: 'payments.paymentAccountId',
        to: 'paymentAccounts.id',
      },
    },
    marketplaceUser: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').PaymentAccount,
      join: {
        from: 'payments.marketplaceUserId',
        to: 'marketplaceUsers.id',
      },
    },
    paymentInstrument: {
      relation: Model.HasOneRelation,
      modelClass: require('.').PaymentInstrument,
      join: {
        from: 'payments.paymentInstrumentId',
        to: 'paymentInstruments.id',
      },
    },
  });

  $parseDatabaseJson(json: Pojo): Pojo {
    json = super.$parseDatabaseJson(json);
    BaseModel.toNumber(json, 'amount');
    BaseModel.toNumber(json, 'amountRequested');
    BaseModel.toNumber(json, 'amountReversed');
    return json;
  }
}
