import {
  FilterNumberFields,
  FilterStringFields,
  SortDirection,
} from '../../common/sqldb/types';

export enum PaymentAccountState {
  ONBOARDING = 'ONBOARDING',
  PROVISIONING = 'PROVISIONING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CREATED = 'CREATED',
}

export enum CheckoutItemType {
  PROCEDURE = 'procedure',
  MEMBERSHIP = 'membership',
  PACKAGE = 'package',
  GRATUITY = 'gratuity',
  PACKAGE_CREDIT = 'package_credit',
  DISCOUNT = 'discount',
  TRAVEL_FEE = 'travel_fee',
  REFUND = 'refund',
  OTHER = 'other',
}

export enum PaymentType {
  CREDIT = 'credit',
}

export enum PaymentStatus {
  ACCEPTED = 'accepted',
  AUTHORIZED = 'authorized',
  FAILED = 'failed',
  PENDING = 'pending',
}

export enum PayoutStatus {
  ACCEPTED = 'accepted',
  FAILED = 'failed',
  PENDING = 'pending',
}

export enum PaymentAccountPlatform {
  FINIX = 'finix',
}

export interface PaymentAccountFields {
  label: string;
  platform: string;
  enabled: boolean;
}

export interface FinixAccountFields {
  identityId?: string;
  merchantId?: string;
  onboardingStatus?: string;
  onboardingFormId?: string;
}

export interface CheckoutFields {
  total: number;
  paid: number;
  balance: number;
}

export interface CheckoutItemFields {
  quantity: number;
  price: number;
  type: CheckoutItemType;
  description: string;
  payout?: number;
  key?: string;
}

export interface PaymentFields {
  status: PaymentStatus;
  description: string;
  amount: number;
  amountRequested: number;
  amountReversed?: number;
}

export enum PaymentInstrumentType {
  CARD = 'card',
}

export interface PaymentInstrumentFields {
  type: PaymentInstrumentType;
  cardType?: string;
  processor: PaymentAccountPlatform;
  brand?: string;
  lastFour?: string;
  expirationMonth?: number;
  expirationYear?: number;
}

export interface PayoutFields {
  status: PayoutStatus;
  description: string;
  amount: number;
  amountRequested: number;
  amountReversed?: number;
}

export enum CheckoutSortField {
  ID = 'id',
  TOTAL = 'total',
  PAID = 'paid',
  BALANCE = 'balance',
  SUMMARY = 'summary',
  FULLNAME = 'fullName',
  MARKETPLACEID = 'marketplaceId',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface CheckoutSortFields {
  field: CheckoutSortField;
  direction: SortDirection;
}

export interface CheckoutFilterFields {
  id?: FilterNumberFields;
  total?: FilterNumberFields;
  paid?: FilterNumberFields;
  balance?: FilterNumberFields;
  marketplaceId?: FilterNumberFields;
  fullName?: FilterStringFields;
  summary?: FilterStringFields;
}

export enum PayoutSortField {
  ID = 'id',
  STATUS = 'status',
  DESCRIPTION = 'description',
  AMOUNT = 'amount',
  AMOUNTREQUESTED = 'amountRequested',
  AMOUNTREVERSED = 'amountReversed',
  MARKETPLACEID = 'marketplaceId',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface PayoutSortFields {
  field: PayoutSortField;
  direction: SortDirection;
}

export interface PayoutFilterFields {
  id?: FilterNumberFields;
  status?: FilterStringFields;
  description?: FilterStringFields;
  amount?: FilterNumberFields;
  amountRequested?: FilterNumberFields;
  amountReversed?: FilterNumberFields;
  marketplaceId?: FilterNumberFields;
}
