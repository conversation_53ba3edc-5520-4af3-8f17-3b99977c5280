import { Express } from 'express';
import jwt from 'jsonwebtoken';
import { CustomRequest } from '../../api/context';
import { getConfig } from '../../config';
import { SqlDbSource } from '../../datasources';
import { ParticipantType } from '../appointment/sqldb/types';
import { uploadFormAttachment } from '../emr/forms';
import { FormType } from '../emr/sqldb/types';
import SlackLogger from '../logger/SlackLogger';
import {
  QualiphyJwtPayload,
  onUpdated,
  processQualiphyWebhook,
  updateQualiphyInvitation,
} from './invite';
import { QualiphyInvitationStatus } from './sqldb/types';

export type QuestionAnswer = {
  question: string;
  answers: string[];
  answer: string[];
  responses: string[];
};

type QualiphyWebhookRequest = {
  provider_name?: string;
  clinic_name?: string;
  exam_name?: string;
  exam_status?: string;
  exam_url?: string;
  exam_id?: string;
  patient_exam_id?: string;
  reason?: string;
  additional_data?: string;
  question_answers?: QuestionAnswer[];
};

export default (app: Express): void => {
  app.post('/webhooks/qualiphy', async (req, res) => {
    const customReq = req as CustomRequest;
    const sqldb = new SqlDbSource(customReq.context.knex);
    const body = req.body as QualiphyWebhookRequest;

    const {
      exam_status,
      additional_data,
      exam_id,
      exam_url,
      reason,
      exam_name,
      clinic_name,
      provider_name,
      question_answers,
    } = body ?? {};

    console.log(
      [
        '/webhooks/qualiphy',
        `exam_id: ${exam_id}`,
        `exam_name: ${exam_name}`,
        `clinic_name: ${clinic_name}`,
        `provider_name: ${provider_name}`,
        `exam_status: ${exam_status}`,
        `reason: ${reason}`,
      ].join('; '),
    );

    if (!additional_data) {
      res.status(404).json({ message: 'Not authorized' }).end();
      return;
    }

    let inviteId;

    try {
      const { token } = JSON.parse(additional_data);

      const decoded = jwt.verify(token, getConfig().auth.secret, {
        maxAge: '90d',
      }) as QualiphyJwtPayload;

      inviteId = decoded.inviteId;
    } catch (err) {
      res.status(404).json({ message: 'Not authorized' }).end();
      return;
    }

    try {
      const invite = await sqldb.qualiphyInvitation(inviteId);

      if (!invite) {
        res.status(404).json({ message: 'Invitation not found' }).end();
        return;
      }

      if (
        invite.exams?.find((e) => e.qualiphyId === Number(exam_id))?.status !==
        QualiphyInvitationStatus.PENDING
      ) {
        res
          .status(400)
          .json({
            message: 'Invitation exam has already been processed',
            exam_id,
            invitation_id: inviteId,
          })
          .end();
        return;
      }

      const appointment = await sqldb.appointment(invite.appointmentId);

      if (!appointment) {
        res.status(400).json({ message: 'Appointment not found' }).end();
        return;
      }

      const patientParticipant = appointment?.participants?.find(
        (p) => p.type === ParticipantType.PATIENT,
      );

      const clientProfile = patientParticipant?.clientProfile;
      const patient = await clientProfile?.$relatedQuery('patient', sqldb.knex);
      const forms = await patientParticipant?.$relatedQuery(
        'forms',
        sqldb.knex,
      );
      const form = forms?.find((f) => f.type === FormType.ASSESSMENT);

      if (!form) {
        res.status(400).json({ message: 'Patient form not found' }).end();
        return;
      }

      const update = await updateQualiphyInvitation({
        sqldb,
        invitation: invite,
        exam: {
          id: Number(exam_id),
          status: exam_status ?? '',
          url: exam_url ?? '',
          reason: reason ?? '',
          clinic: clinic_name ?? '',
          provider: provider_name ?? '',
        },
      });

      new SlackLogger({
        sqldb,
        organizationId: invite.organizationId,
      }).send({
        header: `Updated qualiphy invitation status (id:${update?.id})`,
        content: update?.toString() || '',
      });

      if (exam_url && patient) {
        await uploadFormAttachment({
          sqldb,
          filename: `${exam_name} - Qualiphy.pdf`,
          url: exam_url,
          form,
          patientId: patient.id,
        });
      }

      // New logic for processing exam results
      await processQualiphyWebhook({
        sqldb,
        examId: Number(exam_id),
        inviteId,
        questionAnswers: question_answers ?? [],
        patientId: patient?.id,
      });

      await onUpdated({
        sqldb,
        invite: update,
        formId: form.id,
        patientId: patient?.id,
      });

      res.status(200).json({
        message: 'Invitation exam updated',
        invitationId: update?.id,
        exams: update?.exams,
      });
    } catch (err) {
      console.log(err);
      res.json(500).end();
    }
  });
};
