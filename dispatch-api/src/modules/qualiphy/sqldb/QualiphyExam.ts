import { Min } from 'class-validator';
import { Model, RelationMappings } from 'objection';
import { ObjectType, Field, ID, Int, registerEnumType } from 'type-graphql';
import { BaseModel } from '../../common/sqldb';
import { ProcedureDefinition } from '../../procedure/sqldb';
import QualiphyIntegration from './QualiphyIntegration';
import { QualiphyInvitationStatus } from './types';

registerEnumType(QualiphyInvitationStatus, {
  name: 'QualiphyInvitationStatus',
});

@ObjectType()
export default class QualiphyExam extends BaseModel {
  @Field(() => ID)
  readonly id!: number;

  @Field(() => ID)
  qualiphyIntegrationId!: number;

  @Field(() => Int)
  @Min(0)
  index!: number;

  @Field(() => String)
  title!: string;

  @Field(() => ID)
  qualiphyId!: number;

  @Field(() => Int)
  expiresAfter!: number;

  @Field(() => Int)
  refills!: number;

  @Field(() => Boolean)
  archived!: boolean;

  @Field(() => QualiphyInvitationStatus, { nullable: true })
  status?: QualiphyInvitationStatus;

  qualiphyIntegration?: QualiphyIntegration;
  procedureDefinitions?: ProcedureDefinition[];
  info?: string;
  patientExamId?: string;

  static relationMappings = (): RelationMappings => ({
    qualiphyIntegration: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').QualiphyIntegration,
      join: {
        from: 'qualiphyExams.qualiphyIntegrationId',
        to: 'qualiphyIntegrations.id',
      },
    },
    procedureDefinitions: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureDefinition,
      join: {
        from: 'qualiphyExams.id',
        through: {
          from: 'qualiphyExamsProcedureDefinitions.qualiphyExamId',
          to: 'qualiphyExamsProcedureDefinitions.procedureDefinitionId',
        },
        to: 'procedureDefinitions.id',
      },
    },
  });

  static tableName = 'qualiphyExams';
}
